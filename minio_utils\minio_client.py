"""
创建时间：2025.06.23
注释：minio client配置
"""
try:
    from minio import Minio
    from minio.error import S3Error
    MINIO_AVAILABLE = True
except ImportError:
    print("警告: minio包未安装，图片上传功能将不可用")
    print("请运行: pip install minio")
    MINIO_AVAILABLE = False
    Minio = None
    S3Error = Exception

from config import MINIO_ENDPOINT,MINIO_ACCESS_KEY,MINIO_SECRET_KEY,bucket_name
import io
from datetime import timedelta

class MinIOClient:
    def __init__(self):
        """
        初始化minio客户端
        """
        if not MINIO_AVAILABLE:
            raise ImportError("minio包未安装，无法使用MinIO功能。请运行: pip install minio")
            
        self.client = Minio(
            MINIO_ENDPOINT,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False
        )
        self.bucket_name = bucket_name
    
    def ensure_bucket(self,bucket_name:str):
        """ 确保存储桶的存在"""
        try:
            if not self.client.bucket_exists(bucket_name):
                self.client.make_bucket(bucket_name)
                print(f"Bucket '{bucket_name}' created successfully.")
        except S3Error as err:
            print(f"Error creating bucket '{bucket_name}': {err}")


    def upload_bytes(self,data:bytes,bucket_name:str,object_name:str)->str:
        """上传字节流到minio,返回预签名URL"""

        try:
            self.ensure_bucket(bucket_name)
            self.client.put_object(bucket_name,object_name,io.BytesIO(data),len(data))
            url = self.client.presigned_get_object(bucket_name,object_name,expires=timedelta(days=1))
            print(f'上传成功到{bucket_name}/{object_name}')
            return url
        except S3Error as err:
            print(f"Error uploading object '{object_name}': {err}")
            return None

    
    def delete_file(self, bucket_name: str, object_name: str):
        """
        从 MinIO 删除文件
        """
        try:
            self.client.remove_object(bucket_name, object_name)
            print(f"Deleted {bucket_name}/{object_name}")
        except S3Error as e:
            raise Exception(f"MinIO delete failed for {object_name}: {e}")
