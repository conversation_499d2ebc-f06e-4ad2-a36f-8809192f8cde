from base import Base
from sqlalchemy import Column,Integer,String,DateTime,ForeignKey,Text
from datetime import datetime
from sqlalchemy.orm import relationship

# 知识库表  KnowledgeBase
class KnowledgeBase(Base):
    __tablename__ = 'knowledge_base'
    id = Column(Integer,primary_key = True)
    name = Column(String,unique = True)
    description = Column(String)
    created_at = Column(DateTime,default = datetime.now())
    updated_at = Column(DateTime,default = datetime.now(),onupdate = datetime.now())


# 文档表 Document
class Document(Base):
    __tablename__ = 'document'
    id = Column(Integer,primary_key = True)
    kb_id = Column(Integer,ForeignKey('knowledge_base.id'))
    file_name = Column(String)
    chunk_method = Column(String) # 固定分块、语义分块等
    num_chunks = Column(Integer)
    update_date = Column(DateTime,default = datetime.now())
    minio_path = Column(String)

    kb = relationship("KnowledgeBase",back_populates = "documents")
    

# 分块表 chunk
class Chunk(Base):
    __tablename__ = 'chunk'
    id = Column(Integer,primary_key = True)
    kb_id = Column(Integer,ForeignKey('document.id'))
    chunk_index = Column(Integer) # 固定分块、语义分块等
    content = Column(Text)
    update_date = Column(DateTime,default = datetime.now())

    kb = relationship("Document",back_populates = "chunks")