from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import  sessionmaker
from config import connection

# 创建sqlalchemy基类
Base = declarative_base()

# 创建数据库引擎
engine = create_engine(connection,echo=False) # echo=True是表示打印sql日志

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    """
    提供数据库会话，依赖注入到FastAPI路由
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def  init_db():
    """
    初始化数据库，创建所有表
    """
    Base.meatadata.create_all(bind = engine)