"""
查询知识库记录
用户输入query
"""

from fastapi import APIRouter,HTTPException
from pydantic import BaseModel
from typing import List, Optional
import json
import logging
try:
    # 尝试相对导入（当作为模块运行时）
    from .milvus import milvus_client
except ImportError:
    # 尝试绝对导入（当直接运行时）
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from operation.milvus import milvus_client

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/query", tags=["用户查询"])

class Query(BaseModel):
    # 用户问题
    query:str
    # 知识库的id
    rag_id:List[str]

class Response(BaseModel):
    # 回答
    answer: str
    # 文本块来源
    sources: List[dict]
    # 相关图片信息
    images: List[dict] = []


@router.post("/", response_model=Response)
async def query_endpoint(request: Query):
    try:
        # 1. milvus查询获取的内容
        content_list = milvus_client.query(request.query, request.rag_id, limit=5)
        
        if not content_list:
            raise HTTPException(status_code=404, detail="No relevant content found in Milvus")

        # 2. 整合内容并提取图片信息
        combined_content = "\n\n".join([item["content"] for item in content_list])

        # 提取所有相关图片
        all_images = []
        for item in content_list:
            if item.get("has_images") and item.get("image_urls"):
                try:
                    image_data = json.loads(item["image_urls"]) if isinstance(item["image_urls"], str) else item["image_urls"]
                    if isinstance(image_data, list):
                        for img in image_data:
                            if isinstance(img, dict) and img.get("url"):
                                # 生成LLM友好的图片引用
                                img_description = img.get("alt_text") or img.get("caption") or "相关图片"
                                if img.get("page_number"):
                                    img_description += f"(第{img['page_number']}页)"
                                elif img.get("slide_number"):
                                    img_description += f"(第{img['slide_number']}张幻灯片)"

                                img_reference = f"{img_description}:img[{img['url']}]"
                                all_images.append(img_reference)
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"解析图片信息失败: {str(e)}")

        # 3. 构建包含图片引用的提示词
        image_instruction = ""
        if all_images:
            image_list = "\n".join([f"- {img}" for img in all_images])
            image_instruction = f"\n\n相关图片资源：\n{image_list}\n\n请在回答中适当引用相关图片，使用格式：描述:img[链接]"

        prompt = [
            {"role": "system", "content": "你是一位知识库问答专家。当回答中涉及到图片时，请使用格式'描述:img[链接]'来引用图片，这样前端可以正确解析和展示图片。"},
            {
                "role": "user",
                "content": f"请基于以下内容：\n{combined_content}{image_instruction}\n\n请回答以下问题：{request.query}\n\n要求：\n1. 尽量全面概括，不要回答除问题以外的内容\n2. 如果内容涉及图片，请在适当位置使用'描述:img[链接]'格式引用图片\n3. 图片引用应该自然地融入到回答文本中"
            }
        ]

        # 4. Call LLM to generate answer
        response = milvus_client.client.chat.completions.create(
            model=milvus_client.llm_model_name,
            messages=prompt,
            max_tokens=500,  # Adjust as needed
            temperature=0.7  # Adjust as needed
        )
        answer = response.choices[0].message.content.strip()

        # 5. Format sources for response
        sources = []

        for item in content_list:
            # 处理文本来源
            sources.append({
                "id": item["id"],
                "rag_id": item["rag_id"],
                "source": item["source"],
                "chunk_id": item["chunk_id"],
                "score": item["score"],
                "content": item["content"][:200],  # Truncate for brevity
                "has_images": item.get("has_images", False)
            })

        # 6. 提取回答中的图片链接信息（用于前端解析）
        embedded_images = []
        if ":img[" in answer:
            # 解析回答中的图片引用
            import re
            pattern = r'([^:]+):img\[([^\]]+)\]'
            matches = re.findall(pattern, answer)
            for description, url in matches:
                embedded_images.append({
                    "description": description.strip(),
                    "url": url.strip(),
                    "embedded": True
                })

        # 7. Return structured response
        return Response(
            answer=answer,
            sources=sources,
            images=embedded_images  # 返回嵌入在回答中的图片信息
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Query failed: {str(e)}")