#!/usr/bin/env python3
"""
测试删除API的示例脚本
"""
import requests
import json

def test_delete_api():
    """测试删除API"""
    base_url = "http://localhost:3008"  # 根据你的配置调整端口
    
    # 测试数据
    test_data = {
        "rag_ids": ["test_rag_1", "test_rag_2"],
        "doc_ids": ["test_doc_1", "test_doc_2"]
    }
    
    print("测试删除API...")
    print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            f"{base_url}/delete/",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            print("✓ API调用成功")
        else:
            print("⚠ API调用返回错误状态码")
            
    except requests.exceptions.ConnectionError:
        print("⚠ 无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

if __name__ == "__main__":
    test_delete_api()
