"""
milvus类的定义
"""
from pymilvus import DataType, connections, MilvusClient, Function, FunctionType
from sentence_transformers import SentenceTransformer
from openai import OpenAI
from config import EMBEDDING_MODEL_PATH, MILVUS_HOST, MILVUS_PORT,\
    LLM_API_KEY, LLM_BASE_URL, LLM_MODEL_NAME, vector_dim
from config import collection_name

class MilvusVanna:
    def __init__(self, config=None):
        config = config or {}

        # 初始化本地嵌入模型
        try:
            embedding_model_path = config.get("embedding_model_path", EMBEDDING_MODEL_PATH)
            self.embedding_model = SentenceTransformer(embedding_model_path)
            print(f"Loaded SentenceTransformer model: {embedding_model_path}")
        except Exception as e:
            print(f"Failed to load SentenceTransformer model: {e}")
            raise

        self.host = config.get("host", MILVUS_HOST)
        self.port = config.get("port", MILVUS_PORT)
        self.vector_dim = config.get("vector_dim", vector_dim)
        self.collection_name = config.get("collection_name", collection_name)

        # 初始化 Milvus 连接
        connections.connect(host=self.host, port=self.port)

        # LLM 配置
        self.llm_api_key = config.get("llm_api_key", LLM_API_KEY)
        self.llm_base_url = config.get("llm_base_url", LLM_BASE_URL)
        self.llm_model_name = config.get("llm_model_name", LLM_MODEL_NAME)
        self.client = OpenAI(api_key=self.llm_api_key, base_url=self.llm_base_url)
        self.config = config

    def _get_or_create_collection(self, name: str):
        # 用 MilvusClient 新 API 创建/获取 collection
        client = MilvusClient(uri=f"http://{self.host}:{self.port}")
        if name not in client.list_collections():
            schema = client.create_schema()
            schema.add_field("id", DataType.VARCHAR, max_length=128, is_primary=True)
            # 文本分块后的内容
            schema.add_field("content", DataType.VARCHAR, max_length=65535, enable_analyzer=True)
            # 知识库的id
            schema.add_field('rag_id', DataType.VARCHAR, max_length=128)
            # 文件来源
            schema.add_field("source", DataType.VARCHAR, max_length=128)
            # 上传文件的id
            schema.add_field("doc_id", DataType.VARCHAR, max_length=128)
            # 分块的id
            schema.add_field("chunk_id", DataType.VARCHAR, max_length=128)
            # dense:理解用户意图，找到语义相关内容
            schema.add_field("dense", DataType.FLOAT_VECTOR, dim=self.vector_dim)
            # 精确匹配关键词，确保相关性
            schema.add_field("sparse", DataType.SPARSE_FLOAT_VECTOR)
            schema.add_field("content_hash", DataType.VARCHAR, max_length=32)
            # 图片相关字段
            schema.add_field("image_urls", DataType.VARCHAR, max_length=65535)  # 存储图片URL列表（JSON格式）
            schema.add_field("has_images", DataType.BOOL)  # 标识是否包含图片

            bm25_func = Function(
                name="content_bm25_emb",
                input_field_names=["content"],
                output_field_names=["sparse"],
                function_type=FunctionType.BM25,
            )
            schema.add_function(bm25_func)

            index_params = client.prepare_index_params()
            index_params.add_index(
                field_name="dense",
                index_type="AUTOINDEX",
                metric_type="IP"
            )
            index_params.add_index(
                field_name="sparse",
                index_type="SPARSE_INVERTED_INDEX",
                metric_type="BM25"
            )
            index_params.add_index(
                field_name="id",
                index_type="INVERTED"
            )
            index_params.add_index(
                field_name="content_hash",
                index_type="INVERTED"
            )
            index_params.add_index(
                field_name="has_images",
                index_type="INVERTED"
            )

            client.create_collection(
                collection_name=name,
                schema=schema,
                index_params=index_params
            )
        return client
    
    def query(self, query_text: str, rag_ids: list, limit: int = 10):
        """
        执行向量检索查询，根据用户查询和RAG ID列表，从Milvus中检索相关内容。
        优先尝试混合检索，如果失败则回退到dense向量检索。

        Args:
            query_text (str): 用户输入的查询文本
            rag_ids (list): RAG ID列表，用于过滤数据
            limit (int): 返回结果的最大数量，默认为10

        Returns:
            list: 包含检索结果的列表，每个结果包括id, content, rag_id, source, chunk_id和分数
        """
        # 首先尝试混合检索
        try:
            return self._hybrid_search(query_text, rag_ids, limit)
        except Exception as e:
            print(f"混合检索失败: {e}")
            print("回退到dense向量检索...")
            return self._dense_search(query_text, rag_ids, limit)

    def _hybrid_search(self, query_text: str, rag_ids: list, limit: int = 10):
        """尝试执行混合检索"""
        # 获取或创建collection
        client = self._get_or_create_collection(collection_name)
        
        try:
            # 生成查询的dense embedding
            query_embedding = self.embedding_model.encode([query_text])[0].tolist()

            # 构建过滤表达式
            rag_ids_str = ', '.join([f'"{rag_id}"' for rag_id in rag_ids])
            filter_expr = f"rag_id in [{rag_ids_str}]"

            # 尝试使用新版本的hybrid_search API
            try:
                from pymilvus import AnnSearchRequest, RRFRanker

                # 构建dense向量搜索请求
                dense_search_req = AnnSearchRequest(
                    data=[query_embedding],
                    anns_field="dense",
                    param={"metric_type": "IP", "params": {}},
                    limit=limit,
                    expr=filter_expr
                )

                # 构建sparse向量搜索请求（BM25）
                sparse_search_req = AnnSearchRequest(
                    data=[query_text],
                    anns_field="sparse",
                    param={"metric_type": "BM25", "params": {}},
                    limit=limit,
                    expr=filter_expr
                )

                # 使用RRF (Reciprocal Rank Fusion) ranker
                ranker = RRFRanker()

                # 执行混合检索
                results = client.hybrid_search(
                    collection_name=collection_name,
                    reqs=[dense_search_req, sparse_search_req],
                    ranker=ranker,
                    limit=limit,
                    output_fields=["id", "content", "rag_id", "source", "doc_id", "chunk_id", "image_urls", "has_images"]
                )

            except (ImportError, Exception) as e:
                # 如果新API不可用或出错，回退到普通的dense向量搜索
                print(f"Warning: hybrid_search失败 ({e})，回退到dense向量搜索")
                results = client.search(
                    collection_name=collection_name,
                    data=[query_embedding],
                    anns_field="dense",
                    param={"metric_type": "IP", "params": {}},
                    limit=limit,
                    expr=filter_expr,
                    output_fields=["id", "content", "rag_id", "source", "doc_id", "chunk_id", "image_urls", "has_images"]
                )
            
            # 格式化结果
            formatted_results = []

            # 处理搜索结果
            if results and len(results) > 0:
                # 获取第一个结果集
                result_set = results[0] if isinstance(results[0], list) else results

                for result in result_set:
                    # 处理不同的结果格式
                    if hasattr(result, 'entity'):
                        # 新版本格式
                        entity = result.entity
                        formatted_results.append({
                            "id": entity.get("id"),
                            "content": entity.get("content"),
                            "rag_id": entity.get("rag_id"),
                            "source": entity.get("source"),
                            "doc_id": entity.get("doc_id"),
                            "chunk_id": entity.get("chunk_id"),
                            "image_urls": entity.get("image_urls"),
                            "has_images": entity.get("has_images", False),
                            "score": result.distance if hasattr(result, 'distance') else result.score
                        })
                    else:
                        # 旧版本格式或字典格式
                        formatted_results.append({
                            "id": result.get("id"),
                            "content": result.get("content"),
                            "rag_id": result.get("rag_id"),
                            "source": result.get("source"),
                            "doc_id": result.get("doc_id"),
                            "chunk_id": result.get("chunk_id"),
                            "image_urls": result.get("image_urls"),
                            "has_images": result.get("has_images", False),
                            "score": result.get("distance", result.get("score", 0))
                        })

            return formatted_results

        except Exception as e:
            print(f"Hybrid search failed: {e}")
            raise
        finally:
            client.close()  # 关闭 Milvus 客户端连接

    def _dense_search(self, query_text: str, rag_ids: list, limit: int = 10):
        """执行简化的dense向量检索"""
        # 获取或创建collection
        client = self._get_or_create_collection(collection_name)

        try:
            # 生成查询的dense embedding
            query_embedding = self.embedding_model.encode([query_text])[0].tolist()

            # 构建过滤表达式
            rag_ids_str = ', '.join([f'"{rag_id}"' for rag_id in rag_ids])
            filter_expr = f"rag_id in [{rag_ids_str}]"

            # 执行dense向量搜索
            results = client.search(
                collection_name=collection_name,
                data=[query_embedding],
                anns_field="dense",
                param={"metric_type": "IP", "params": {}},
                limit=limit,
                expr=filter_expr,
                output_fields=["id", "content", "rag_id", "source", "doc_id", "chunk_id", "image_urls", "has_images"]
            )

            # 格式化结果
            formatted_results = []

            if results and len(results) > 0:
                for result in results[0]:  # search返回的是列表的列表
                    # 处理不同的结果格式
                    if hasattr(result, 'entity'):
                        # 新版本格式
                        entity = result.entity
                        formatted_results.append({
                            "id": entity.get("id"),
                            "content": entity.get("content"),
                            "rag_id": entity.get("rag_id"),
                            "source": entity.get("source"),
                            "doc_id": entity.get("doc_id"),
                            "chunk_id": entity.get("chunk_id"),
                            "image_urls": entity.get("image_urls"),
                            "has_images": entity.get("has_images", False),
                            "score": result.distance if hasattr(result, 'distance') else 0
                        })
                    else:
                        # 旧版本格式或字典格式
                        formatted_results.append({
                            "id": result.get("id"),
                            "content": result.get("content"),
                            "rag_id": result.get("rag_id"),
                            "source": result.get("source"),
                            "doc_id": result.get("doc_id"),
                            "chunk_id": result.get("chunk_id"),
                            "image_urls": result.get("image_urls"),
                            "has_images": result.get("has_images", False),
                            "score": result.get("distance", result.get("score", 0))
                        })

            return formatted_results

        except Exception as e:
            print(f"Dense search failed: {e}")
            raise
        finally:
            client.close()  # 关闭 Milvus 客户端连接


milvus_client = MilvusVanna()