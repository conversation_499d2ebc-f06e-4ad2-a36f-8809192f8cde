"""
删除知识库的记录
支持按知识库、文档、分块等不同级别删除记录
"""
try:
    # 尝试相对导入（当作为模块运行时）
    from .milvus import MilvusVanna
except ImportError:
    # 尝试绝对导入（当直接运行时）
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from operation.milvus import MilvusVanna
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/delete", tags=["删除记录"])

class DeleteRequest(BaseModel):
    """删除请求模型"""
    # 知识库ID列表 - 删除整个知识库的所有记录
    rag_ids: Optional[List[str]] = None
    # 文档ID列表 - 删除指定文档的所有记录
    doc_ids: Optional[List[str]] = None

class DeleteResponse(BaseModel):
    """删除响应模型"""
    success: bool
    deleted_count: int
    message: str
    details: dict


class MilvusDelete(MilvusVanna):
    def __init__(self, config=None):
        super().__init__(config)

    def delete_by_rag_ids(self, collection_name: str, rag_ids: List[str]) -> int:
        """
        按知识库ID删除记录

        Args:
            collection_name (str): 集合名称
            rag_ids (List[str]): 知识库ID列表

        Returns:
            int: 删除的记录数量
        """
        if not rag_ids:
            return 0

        client = self._get_or_create_collection(collection_name)

        try:
            # 构建过滤条件
            rag_id_filter = " or ".join([f"rag_id == '{rag_id}'" for rag_id in rag_ids])
            result = client.delete(collection_name=collection_name, filter=rag_id_filter)
            return result.get('delete_count', 0)
        except Exception as e:
            logger.error(f"删除知识库记录失败: {str(e)}")
            raise Exception(f"Failed to delete records by rag_ids: {str(e)}")
        finally:
            client.close()

    def delete_by_doc_ids(self, collection_name: str, doc_ids: List[str]) -> int:
        """
        按文档ID删除记录

        Args:
            collection_name (str): 集合名称
            doc_ids (List[str]): 文档ID列表

        Returns:
            int: 删除的记录数量
        """
        if not doc_ids:
            return 0

        client = self._get_or_create_collection(collection_name)

        try:
            # 构建过滤条件
            doc_id_filter = " or ".join([f"doc_id == '{doc_id}'" for doc_id in doc_ids])
            result = client.delete(collection_name=collection_name, filter=doc_id_filter)
            return result.get('delete_count', 0)
        except Exception as e:
            logger.error(f"删除文档记录失败: {str(e)}")
            raise Exception(f"Failed to delete records by doc_ids: {str(e)}")
        finally:
            client.close()

    def delete_records(self,collection_name, request: DeleteRequest) -> DeleteResponse:
        """
        统一删除接口，支持多种删除方式

        Args:
            request (DeleteRequest): 删除请求

        Returns:
            DeleteResponse: 删除结果
        """
        if not any([request.rag_ids, request.doc_ids, request.chunk_ids, request.record_ids]):
            raise ValueError("至少需要提供一种删除条件")

        total_deleted = 0
        details = {}

        try:
            # 按知识库ID删除
            if request.rag_ids:
                deleted_count = self.delete_by_rag_ids(collection_name, request.rag_ids)
                total_deleted += deleted_count
                details['rag_ids_deleted'] = deleted_count
                logger.info(f"按知识库ID删除了 {deleted_count} 条记录")

            # 按文档ID删除
            if request.doc_ids:
                deleted_count = self.delete_by_doc_ids(collection_name, request.doc_ids)
                total_deleted += deleted_count
                details['doc_ids_deleted'] = deleted_count
                logger.info(f"按文档ID删除了 {deleted_count} 条记录")

            return DeleteResponse(
                success=True,
                deleted_count=total_deleted,
                message=f"成功删除 {total_deleted} 条记录",
                details=details
            )

        except Exception as e:
            logger.error(f"删除操作失败: {str(e)}")
            return DeleteResponse(
                success=False,
                deleted_count=0,
                message=f"删除失败: {str(e)}",
                details=details
            )

# 创建删除实例
milvus_delete = MilvusDelete()

@router.post("/", response_model=DeleteResponse)
async def delete_records_endpoint(request: DeleteRequest):
    """
    删除记录API端点

    支持以下删除方式：
    1. 按知识库ID删除：删除整个知识库的所有记录
    2. 按文档ID删除：删除指定文档的所有记录

    可以同时使用多种删除方式
    """
    try:
        result = milvus_delete.delete_records(request)

        if not result.success:
            raise HTTPException(status_code=500, detail=result.message)

        return result

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"删除API调用失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除操作失败: {str(e)}")