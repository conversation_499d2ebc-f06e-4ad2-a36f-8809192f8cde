"""
创建时间：2025.06.23
注释：图片处理工具类，支持从各种文档格式中提取图片并保存到MinIO
"""
import os
import io
import uuid
import hashlib
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import logging

try:
    from PIL import Image
except ImportError:
    Image = None

try:
    import fitz  # PyMuPDF
except ImportError:
    fitz = None

try:
    import docx
    from docx.document import Document
    from docx.oxml.table import CT_Tbl
    from docx.oxml.text.paragraph import CT_P
    from docx.table import _Cell, Table
    from docx.text.paragraph import Paragraph
except ImportError:
    docx = None

try:
    from pptx import Presentation
    from pptx.enum.shapes import MSO_SHAPE_TYPE
except ImportError:
    Presentation = None

# 修复导入问题 - 使用绝对导入
import sys
import os
# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入MinIO客户端
from minio_utils.minio_client import MinIOClient


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ImageInfo:
    """图片信息类"""
    image_id: str  # 图片唯一ID
    filename: str  # 原始文件名
    minio_url: str  # MinIO存储URL
    page_number: Optional[int] = None  # 页码（PDF/PPT）
    slide_number: Optional[int] = None  # 幻灯片编号（PPT）
    position: Optional[str] = None  # 图片在文档中的位置描述
    alt_text: Optional[str] = None  # 图片替代文本
    caption: Optional[str] = None  # 图片标题
    size: Optional[Tuple[int, int]] = None  # 图片尺寸 (width, height)
    format: Optional[str] = None  # 图片格式

class ImageProcessor:
    """图片处理器类，支持从各种文档格式中提取图片"""

    def __init__(self, minio_client: Optional[MinIOClient] = None):
        """
        初始化图片处理器

        Args:
            minio_client: MinIO客户端实例，如果为None则创建新实例
        """
        try:
            self.minio_client = minio_client or MinIOClient()
            self.minio_available = True
        except ImportError as e:
            logger.warning(f"MinIO客户端初始化失败: {str(e)}")
            self.minio_client = None
            self.minio_available = False

        self.supported_formats = {
            'pdf': self._extract_from_pdf,
            'docx': self._extract_from_docx,
            'doc': self._extract_from_docx,  # 使用相同的处理方法
            'pptx': self._extract_from_pptx,
            'ppt': self._extract_from_pptx   # 使用相同的处理方法
        }
    
    def extract_images_from_file(self, file_path: str, file_type: str,
                                doc_id: str) -> List[ImageInfo]:
        """
        从文件中提取图片

        Args:
            file_path: 文件路径
            file_type: 文件类型
            doc_id: 文档ID，用于生成图片存储路径

        Returns:
            提取的图片信息列表
        """
        if not self.minio_available:
            logger.warning("MinIO不可用，跳过图片提取")
            return []

        try:
            file_type = file_type.lower()
            if file_type not in self.supported_formats:
                logger.warning(f"不支持的文件类型: {file_type}")
                return []

            extract_func = self.supported_formats[file_type]
            return extract_func(file_path, doc_id)

        except Exception as e:
            logger.error(f"从文件 {file_path} 提取图片失败: {str(e)}")
            return []
    
    def _extract_from_pdf(self, file_path: str, doc_id: str) -> List[ImageInfo]:
        """从PDF文件中提取图片"""
        if fitz is None:
            logger.error("PyMuPDF 未安装，无法处理PDF文件")
            return []
        
        images = []
        try:
            pdf_document = fitz.open(file_path)
            
            for page_num in range(len(pdf_document)):
                page = pdf_document.load_page(page_num)
                image_list = page.get_images()
                
                for img_index, img in enumerate(image_list):
                    try:
                        # 获取图片数据
                        xref = img[0]
                        pix = fitz.Pixmap(pdf_document, xref)
                        
                        if pix.n - pix.alpha < 4:  # 确保是RGB或灰度图
                            # 转换为PNG格式的字节数据
                            img_data = pix.tobytes("png")
                            
                            # 生成图片信息
                            image_info = self._save_image_to_minio(
                                img_data, doc_id, f"pdf_page_{page_num+1}_img_{img_index+1}",
                                page_number=page_num+1, format="png"
                            )
                            
                            if image_info:
                                images.append(image_info)
                        
                        pix = None  # 释放内存
                        
                    except Exception as e:
                        logger.error(f"处理PDF第{page_num+1}页第{img_index+1}张图片失败: {str(e)}")
                        continue
            
            pdf_document.close()
            
        except Exception as e:
            logger.error(f"处理PDF文件失败: {str(e)}")
        
        return images
    
    def _extract_from_docx(self, file_path: str, doc_id: str) -> List[ImageInfo]:
        """从Word文档中提取图片"""
        if docx is None:
            logger.error("python-docx 未安装，无法处理Word文件")
            return []
        
        images = []
        try:
            doc = docx.Document(file_path)
            
            # 提取文档中的图片
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    try:
                        # 获取图片数据
                        img_data = rel.target_part.blob
                        
                        # 获取图片格式
                        img_format = rel.target_ref.split('.')[-1].lower()
                        
                        # 生成图片信息
                        image_info = self._save_image_to_minio(
                            img_data, doc_id, f"docx_img_{rel.target_ref}",
                            format=img_format
                        )
                        
                        if image_info:
                            images.append(image_info)
                            
                    except Exception as e:
                        logger.error(f"处理Word文档图片失败: {str(e)}")
                        continue
        
        except Exception as e:
            logger.error(f"处理Word文档失败: {str(e)}")
        
        return images
    
    def _extract_from_pptx(self, file_path: str, doc_id: str) -> List[ImageInfo]:
        """从PowerPoint文档中提取图片"""
        if Presentation is None:
            logger.error("python-pptx 未安装，无法处理PowerPoint文件")
            return []
        
        images = []
        try:
            prs = Presentation(file_path)
            
            for slide_num, slide in enumerate(prs.slides, 1):
                for shape_index, shape in enumerate(slide.shapes):
                    try:
                        if shape.shape_type == MSO_SHAPE_TYPE.PICTURE:
                            # 获取图片数据
                            img_data = shape.image.blob
                            
                            # 获取图片格式
                            img_format = shape.image.ext[1:]  # 移除点号
                            
                            # 生成图片信息
                            image_info = self._save_image_to_minio(
                                img_data, doc_id, 
                                f"pptx_slide_{slide_num}_img_{shape_index+1}",
                                slide_number=slide_num, format=img_format
                            )
                            
                            if image_info:
                                images.append(image_info)
                                
                    except Exception as e:
                        logger.error(f"处理PPT第{slide_num}张幻灯片图片失败: {str(e)}")
                        continue
        
        except Exception as e:
            logger.error(f"处理PowerPoint文档失败: {str(e)}")
        
        return images
    
    def _save_image_to_minio(self, img_data: bytes, doc_id: str, 
                           base_filename: str, **kwargs) -> Optional[ImageInfo]:
        """
        将图片保存到MinIO并返回图片信息
        
        Args:
            img_data: 图片字节数据
            doc_id: 文档ID
            base_filename: 基础文件名
            **kwargs: 其他图片信息
            
        Returns:
            图片信息对象
        """
        try:
            # 生成唯一的图片ID
            image_id = str(uuid.uuid4())
            
            # 生成文件名
            img_format = kwargs.get('format', 'png')
            filename = f"{doc_id}/{base_filename}_{image_id}.{img_format}"
            
            # 上传到MinIO
            minio_url = self.minio_client.upload_bytes(
                data=img_data,
                bucket_name=self.minio_client.bucket_name,
                object_name=filename
            )
            
            if minio_url:
                # 获取图片尺寸（如果可能）
                size = None
                try:
                    if Image:
                        with Image.open(io.BytesIO(img_data)) as img:
                            size = img.size
                except Exception:
                    pass
                
                return ImageInfo(
                    image_id=image_id,
                    filename=filename,
                    minio_url=minio_url,
                    page_number=kwargs.get('page_number'),
                    slide_number=kwargs.get('slide_number'),
                    position=kwargs.get('position'),
                    alt_text=kwargs.get('alt_text'),
                    caption=kwargs.get('caption'),
                    size=size,
                    format=img_format
                )
            
        except Exception as e:
            logger.error(f"保存图片到MinIO失败: {str(e)}")
        
        return None
    
    def generate_image_reference(self, image_info: ImageInfo) -> str:
        """
        生成图片引用文本，用于嵌入到文本块中

        Args:
            image_info: 图片信息

        Returns:
            图片引用文本
        """
        reference_parts = [f"[图片: {image_info.image_id}]"]

        if image_info.page_number:
            reference_parts.append(f"(第{image_info.page_number}页)")
        elif image_info.slide_number:
            reference_parts.append(f"(第{image_info.slide_number}张幻灯片)")

        if image_info.alt_text:
            reference_parts.append(f"- {image_info.alt_text}")
        elif image_info.caption:
            reference_parts.append(f"- {image_info.caption}")

        reference_parts.append(f"[链接: {image_info.minio_url}]")

        return " ".join(reference_parts)

    def generate_llm_image_reference(self, image_info: ImageInfo) -> str:
        """
        生成适合LLM回答的图片引用格式

        Args:
            image_info: 图片信息

        Returns:
            LLM图片引用文本，格式：描述:img[链接]
        """
        # 生成图片描述
        description_parts = []

        # 基础描述
        if image_info.alt_text:
            description_parts.append(image_info.alt_text)
        elif image_info.caption:
            description_parts.append(image_info.caption)
        else:
            # 根据文件名生成描述
            filename_base = image_info.filename.split('/')[-1].split('.')[0]
            if 'slide' in filename_base:
                description_parts.append("幻灯片图片")
            elif 'page' in filename_base:
                description_parts.append("页面图片")
            else:
                description_parts.append("相关图片")

        # 添加位置信息
        if image_info.page_number:
            description_parts.append(f"第{image_info.page_number}页")
        elif image_info.slide_number:
            description_parts.append(f"第{image_info.slide_number}张幻灯片")

        # 组合描述
        description = "".join(description_parts) if description_parts else "相关图片"

        # 返回格式：描述:img[链接]
        return f"{description}:img[{image_info.minio_url}]"
