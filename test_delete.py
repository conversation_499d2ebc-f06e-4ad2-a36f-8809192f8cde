#!/usr/bin/env python3
"""
测试删除操作的脚本 - 仅测试方法签名和模型
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_delete_models():
    """测试删除相关的模型和方法签名"""
    print("开始测试删除功能的模型和方法签名...")

    try:
        # 测试导入删除请求模型
        from operation.delete import DeleteRequest, DeleteResponse
        print("✓ 成功导入DeleteRequest和DeleteResponse模型")

        # 测试删除请求模型
        delete_request = DeleteRequest(
            rag_ids=["test_rag_1", "test_rag_2"],
            doc_ids=["test_doc_1", "test_doc_2"]
        )
        print("✓ 成功创建DeleteRequest实例")
        print(f"  - rag_ids: {delete_request.rag_ids}")
        print(f"  - doc_ids: {delete_request.doc_ids}")

        # 测试删除响应模型
        delete_response = DeleteResponse(
            success=True,
            deleted_count=5,
            message="测试删除成功",
            details={"test": "data"}
        )
        print("✓ 成功创建DeleteResponse实例")
        print(f"  - success: {delete_response.success}")
        print(f"  - deleted_count: {delete_response.deleted_count}")
        print(f"  - message: {delete_response.message}")
        print(f"  - details: {delete_response.details}")

        # 测试方法签名（不实际连接Milvus）
        print("\n检查MilvusDelete类的方法签名...")
        import inspect
        from operation.delete import MilvusDelete

        # 检查delete_records方法的签名
        delete_records_sig = inspect.signature(MilvusDelete.delete_records)
        print(f"✓ delete_records方法签名: {delete_records_sig}")

        # 验证参数
        params = list(delete_records_sig.parameters.keys())
        expected_params = ['self', 'request']
        if params == expected_params:
            print("✓ delete_records方法参数正确")
        else:
            print(f"✗ delete_records方法参数不正确，期望: {expected_params}, 实际: {params}")
            return False

        print("\n✓ 所有测试通过！删除功能的方法签名已修复。")
        print("现在可以正常调用 milvus_delete.delete_records(request) 而不需要额外的collection_name参数。")

    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

    return True

if __name__ == "__main__":
    test_delete_models()
